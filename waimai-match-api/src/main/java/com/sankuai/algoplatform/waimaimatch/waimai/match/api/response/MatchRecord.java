package com.sankuai.algoplatform.waimaimatch.waimai.match.api.response;

import java.io.Serializable;

/**
 * 匹配记录类
 */
public class MatchRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 临时门店序号，解析侧关联回传数据用，透传
     */
    private int tempPoiId;

    /**
     * 匹配门店id，匹配时该字段有值
     */
    private String matchId;

    /**
     * 匹配得分，匹配时该字段有值
     */
    private String score;

    /**
     * 不匹配且能够生成时，该字段不为空
     */
    private String newId;

    /**
     * 匹配类型：-1 无效数据，0 不匹配，1 历史匹配，2 即时匹配
     */
    private int matchType;

    // 匹配类型常量
    public static final int MATCH_TYPE_INVALID = -1;    // 无效数据
    public static final int MATCH_TYPE_NO_MATCH = 0;    // 不匹配
    public static final int MATCH_TYPE_HISTORY = 1;     // 历史匹配
    public static final int MATCH_TYPE_REALTIME = 2;    // 即时匹配

    // 构造函数

    /**
     * 默认构造函数
     */
    public MatchRecord() {
    }

    /**
     * 带参数的构造函数
     *
     * @param tempPoiId 临时门店序号
     * @param matchId 匹配门店id
     * @param score 匹配得分
     * @param newId 新生成的id
     * @param matchType 匹配类型
     */
    public MatchRecord(int tempPoiId, String matchId, String score, String newId, int matchType) {
        this.tempPoiId = tempPoiId;
        this.matchId = matchId;
        this.score = score;
        this.newId = newId;
        this.matchType = matchType;
    }

    // Getter and Setter methods

    public int getTempPoiId() {
        return tempPoiId;
    }

    public void setTempPoiId(int tempPoiId) {
        this.tempPoiId = tempPoiId;
    }

    public String getMatchId() {
        return matchId;
    }

    public void setMatchId(String matchId) {
        this.matchId = matchId;
    }

    public String getScore() {
        return score;
    }

    public void setScore(String score) {
        this.score = score;
    }

    public String getNewId() {
        return newId;
    }

    public void setNewId(String newId) {
        this.newId = newId;
    }

    public int getMatchType() {
        return matchType;
    }

    public void setMatchType(int matchType) {
        this.matchType = matchType;
    }

    // 便利方法

    /**
     * 判断是否为有效数据
     *
     * @return true if valid, false otherwise
     */
    public boolean isValid() {
        return matchType != MATCH_TYPE_INVALID;
    }

    /**
     * 判断是否匹配成功
     *
     * @return true if matched, false otherwise
     */
    public boolean isMatched() {
        return matchType == MATCH_TYPE_HISTORY || matchType == MATCH_TYPE_REALTIME;
    }

    /**
     * 判断是否为历史匹配
     *
     * @return true if history match, false otherwise
     */
    public boolean isHistoryMatch() {
        return matchType == MATCH_TYPE_HISTORY;
    }

    /**
     * 判断是否为即时匹配
     *
     * @return true if realtime match, false otherwise
     */
    public boolean isRealtimeMatch() {
        return matchType == MATCH_TYPE_REALTIME;
    }

    /**
     * 判断是否生成了新ID
     *
     * @return true if new ID generated, false otherwise
     */
    public boolean hasNewId() {
        return newId != null && !newId.trim().isEmpty();
    }

    /**
     * 获取匹配类型的描述
     *
     * @return 匹配类型描述
     */
    public String getMatchTypeDescription() {
        switch (matchType) {
            case MATCH_TYPE_INVALID:
                return "无效数据";
            case MATCH_TYPE_NO_MATCH:
                return "不匹配";
            case MATCH_TYPE_HISTORY:
                return "历史匹配";
            case MATCH_TYPE_REALTIME:
                return "即时匹配";
            default:
                return "未知类型";
        }
    }

    @Override
    public String toString() {
        return "MatchRecord{" +
                "tempPoiId=" + tempPoiId +
                ", matchId='" + matchId + '\'' +
                ", score='" + score + '\'' +
                ", newId='" + newId + '\'' +
                ", matchType=" + matchType + " (" + getMatchTypeDescription() + ")" +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        MatchRecord that = (MatchRecord) o;

        if (tempPoiId != that.tempPoiId) return false;
        if (matchType != that.matchType) return false;
        if (matchId != null ? !matchId.equals(that.matchId) : that.matchId != null) return false;
        if (score != null ? !score.equals(that.score) : that.score != null) return false;
        return newId != null ? newId.equals(that.newId) : that.newId == null;
    }

    @Override
    public int hashCode() {
        int result = tempPoiId;
        result = 31 * result + (matchId != null ? matchId.hashCode() : 0);
        result = 31 * result + (score != null ? score.hashCode() : 0);
        result = 31 * result + (newId != null ? newId.hashCode() : 0);
        result = 31 * result + matchType;
        return result;
    }
}
