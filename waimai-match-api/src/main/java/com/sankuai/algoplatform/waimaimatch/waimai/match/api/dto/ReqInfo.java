package com.sankuai.algoplatform.waimaimatch.waimai.match.api.dto;

import java.io.Serializable;
import java.util.List;

/**
 * 请求信息类
 */
public class ReqInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 请求ID
     */
    private Long reqId;

    /**
     * 数据来源，B+商家列表、B+拼团列表等
     */
    private String type;

    /**
     * 是否可以生成新ID
     */
    private Boolean canNewId;

    /**
     * 门店信息列表
     */
    private List<PoiInfo> poiInfos;

    public Long getReqId() {
        return reqId;
    }

    public void setReqId(Long reqId) {
        this.reqId = reqId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Boolean getCanNewId() {
        return canNewId;
    }

    public void setCanNewId(Boolean canNewId) {
        this.canNewId = canNewId;
    }

    public List<PoiInfo> getPoiInfos() {
        return poiInfos;
    }

    public void setPoiInfos(List<PoiInfo> poiInfos) {
        this.poiInfos = poiInfos;
    }

    // 构造函数

    /**
     * 默认构造函数
     */
    public ReqInfo() {
    }

    /**
     * 带参数的构造函数
     *
     * @param reqId 请求ID
     * @param type 请求类型
     * @param canNewId 是否可以生成新ID
     * @param poiInfos 门店信息列表
     */
    public ReqInfo(Long reqId, String type, Boolean canNewId, List<PoiInfo> poiInfos) {
        this.reqId = reqId;
        this.type = type;
        this.canNewId = canNewId;
        this.poiInfos = poiInfos;
    }

    @Override
    public String toString() {
        return "ReqInfo{" +
                "reqId=" + reqId +
                ", type='" + type + '\'' +
                ", canNewId=" + canNewId +
                ", poiInfos=" + (poiInfos != null ? poiInfos.size() + " items" : "null") +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        ReqInfo reqInfo = (ReqInfo) o;

        if (reqId != null ? !reqId.equals(reqInfo.reqId) : reqInfo.reqId != null) return false;
        if (type != null ? !type.equals(reqInfo.type) : reqInfo.type != null) return false;
        if (canNewId != null ? !canNewId.equals(reqInfo.canNewId) : reqInfo.canNewId != null) return false;
        return poiInfos != null ? poiInfos.equals(reqInfo.poiInfos) : reqInfo.poiInfos == null;
    }

    @Override
    public int hashCode() {
        int result = reqId != null ? reqId.hashCode() : 0;
        result = 31 * result + (type != null ? type.hashCode() : 0);
        result = 31 * result + (canNewId != null ? canNewId.hashCode() : 0);
        result = 31 * result + (poiInfos != null ? poiInfos.hashCode() : 0);
        return result;
    }
}
