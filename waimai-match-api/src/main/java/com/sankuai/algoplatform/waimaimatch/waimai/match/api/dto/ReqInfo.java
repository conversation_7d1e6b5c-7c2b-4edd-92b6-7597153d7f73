package com.sankuai.algoplatform.waimaimatch.waimai.match.api.dto;

import java.io.Serializable;
import java.util.List;

public class ReqInfo implements Serializable {
    Long reqId;
    String type;
    Boolean canNewId;
    List<PoiInfo> poiInfos;

    public Long getReqId() {
        return reqId;
    }

    public void setReqId(Long reqId) {
        this.reqId = reqId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Boolean getCanNewId() {
        return canNewId;
    }

    public void setCanNewId(Boolean canNewId) {
        this.canNewId = canNewId;
    }

    public List<PoiInfo> getPoiInfos() {
        return poiInfos;
    }

    public void setPoiInfos(List<PoiInfo> poiInfos) {
        this.poiInfos = poiInfos;
    }
}
